@echo off
title ComfyUI 高级启动器
color 0B
chcp 65001 >nul

:MENU
cls
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ComfyUI 高级启动器                        ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  系统信息:                                                   ║
echo ║  • GPU: NVIDIA GeForce RTX 3090 (24GB VRAM)                 ║
echo ║  • Python: 3.12.10                                          ║
echo ║  • PyTorch: 2.5.1+cu121                                     ║
echo ║                                                              ║
echo ║  启动选项:                                                   ║
echo ║  [1] 标准启动 (自动打开浏览器)                               ║
echo ║  [2] 后台启动 (不打开浏览器)                                 ║
echo ║  [3] 局域网模式 (允许其他设备访问)                           ║
echo ║  [4] 低显存模式 (适用于显存不足)                             ║
echo ║  [5] CPU模式 (不使用GPU)                                     ║
echo ║  [6] 调试模式 (详细日志输出)                                 ║
echo ║  [7] 自定义端口启动                                          ║
echo ║  [8] 检查服务状态                                            ║
echo ║  [9] 停止所有ComfyUI进程                                     ║
echo ║  [0] 退出                                                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
set /p choice=请选择启动方式 (0-9): 

if "%choice%"=="1" goto STANDARD
if "%choice%"=="2" goto BACKGROUND
if "%choice%"=="3" goto NETWORK
if "%choice%"=="4" goto LOWVRAM
if "%choice%"=="5" goto CPU
if "%choice%"=="6" goto DEBUG
if "%choice%"=="7" goto CUSTOM_PORT
if "%choice%"=="8" goto CHECK_STATUS
if "%choice%"=="9" goto KILL_PROCESSES
if "%choice%"=="0" goto EXIT
goto MENU

:STANDARD
echo.
echo ═══════════════════════════════════════
echo 启动模式: 标准启动
echo 访问地址: http://127.0.0.1:8188
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --auto-launch
goto END

:BACKGROUND
echo.
echo ═══════════════════════════════════════
echo 启动模式: 后台启动
echo 访问地址: http://127.0.0.1:8188
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py
goto END

:NETWORK
echo.
echo ═══════════════════════════════════════
echo 启动模式: 局域网模式
echo 本地访问: http://127.0.0.1:8188
echo 局域网访问: http://[您的IP]:8188
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --listen 0.0.0.0 --auto-launch
goto END

:LOWVRAM
echo.
echo ═══════════════════════════════════════
echo 启动模式: 低显存模式
echo 访问地址: http://127.0.0.1:8188
echo 注意: 此模式会降低性能但减少显存使用
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --lowvram --auto-launch
goto END

:CPU
echo.
echo ═══════════════════════════════════════
echo 启动模式: CPU模式
echo 访问地址: http://127.0.0.1:8188
echo 注意: 此模式速度较慢但不需要GPU
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --cpu --auto-launch
goto END

:DEBUG
echo.
echo ═══════════════════════════════════════
echo 启动模式: 调试模式
echo 访问地址: http://127.0.0.1:8188
echo 注意: 将显示详细的调试信息
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --verbose DEBUG --auto-launch
goto END

:CUSTOM_PORT
echo.
set /p port=请输入端口号 (默认8188): 
if "%port%"=="" set port=8188
echo.
echo ═══════════════════════════════════════
echo 启动模式: 自定义端口
echo 访问地址: http://127.0.0.1:%port%
echo ═══════════════════════════════════════
echo.
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --port %port% --auto-launch
goto END

:CHECK_STATUS
echo.
echo ═══════════════════════════════════════
echo 检查ComfyUI服务状态...
echo ═══════════════════════════════════════
echo.
netstat -an | findstr :8188
if %errorlevel%==0 (
    echo ✓ ComfyUI服务正在运行
    echo 访问地址: http://127.0.0.1:8188
) else (
    echo ✗ ComfyUI服务未运行
)
echo.
pause
goto MENU

:KILL_PROCESSES
echo.
echo ═══════════════════════════════════════
echo 正在停止所有ComfyUI进程...
echo ═══════════════════════════════════════
echo.
taskkill /f /im python.exe 2>nul
echo 所有Python进程已停止
echo.
pause
goto MENU

:END
echo.
echo ═══════════════════════════════════════
echo ComfyUI 服务已停止
echo ═══════════════════════════════════════
pause
goto MENU

:EXIT
echo.
echo 感谢使用 ComfyUI 启动器！
timeout /t 2 >nul
exit
