# ComfyUI 部署完成指南

## 🎉 部署状态：成功完成！

ComfyUI已经成功部署在您的系统上，现在可以正常使用了。

## 📋 系统信息

- **Python版本**: 3.12.10
- **PyTorch版本**: 2.5.1+cu121 (支持CUDA)
- **GPU**: NVIDIA GeForce RTX 3090 (24GB VRAM)
- **服务地址**: http://127.0.0.1:8188

## 🚀 启动方式

### 方法1：使用批处理文件（推荐）
双击 `start_comfyui.bat` 文件即可启动

### 方法2：命令行启动
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --auto-launch
```

### 方法3：自定义启动参数
```bash
# 基本启动
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py

# 启动并自动打开浏览器
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --auto-launch

# 监听所有网络接口（允许局域网访问）
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --listen 0.0.0.0

# 使用不同端口
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --port 8080
```

## 🎯 访问地址

启动成功后，在浏览器中访问：
- **本地访问**: http://127.0.0.1:8188
- **局域网访问**: http://[您的IP地址]:8188 （需要使用 --listen 0.0.0.0 参数）

## 📁 重要目录结构

```
ComfyUI-master/
├── models/                    # 模型文件目录
│   ├── checkpoints/          # 主要AI模型（如Stable Diffusion）
│   ├── vae/                  # VAE模型
│   ├── loras/                # LoRA模型
│   ├── controlnet/           # ControlNet模型
│   ├── clip/                 # CLIP模型
│   └── upscale_models/       # 放大模型
├── input/                    # 输入图片目录
├── output/                   # 输出图片目录
├── custom_nodes/             # 自定义节点
└── start_comfyui.bat         # 启动脚本
```

## 📥 下载模型

要使用ComfyUI，您需要下载一些基础模型：

### 基础模型推荐
1. **Stable Diffusion 1.5**: 放入 `models/checkpoints/`
2. **VAE模型**: 放入 `models/vae/`
3. **ControlNet模型**: 放入 `models/controlnet/`

### 模型下载站点
- Hugging Face: https://huggingface.co/
- Civitai: https://civitai.com/
- 官方模型库

## ⚙️ 常用启动参数

| 参数 | 说明 |
|------|------|
| `--auto-launch` | 自动打开浏览器 |
| `--listen 0.0.0.0` | 允许局域网访问 |
| `--port 8080` | 使用自定义端口 |
| `--lowvram` | 低显存模式 |
| `--cpu` | 使用CPU模式 |
| `--preview-method auto` | 启用预览 |
| `--verbose` | 详细日志输出 |

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   - 更改端口：`--port 8080`
   - 或关闭占用8188端口的程序

2. **显存不足**
   - 使用低显存模式：`--lowvram`
   - 或使用CPU模式：`--cpu`

3. **模型加载失败**
   - 检查模型文件是否在正确目录
   - 确认模型文件完整性

4. **网络访问问题**
   - 检查防火墙设置
   - 确认使用了 `--listen 0.0.0.0` 参数

### 查看日志
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --verbose
```

## 🎨 开始使用

1. 启动ComfyUI
2. 在浏览器中打开 http://127.0.0.1:8188
3. 加载或创建工作流
4. 下载并放置所需的模型文件
5. 开始创作！

## 📚 学习资源

- [ComfyUI官方文档](https://docs.comfy.org/)
- [ComfyUI示例工作流](https://comfyanonymous.github.io/ComfyUI_examples/)
- [ComfyUI GitHub](https://github.com/comfyanonymous/ComfyUI)

## 🔄 更新ComfyUI

要更新ComfyUI到最新版本：
```bash
git pull origin master
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install -r requirements.txt --upgrade
```

---

**部署完成时间**: $(Get-Date)
**部署状态**: ✅ 成功
**GPU支持**: ✅ NVIDIA GeForce RTX 3090 (24GB)
**CUDA支持**: ✅ CUDA 12.1
