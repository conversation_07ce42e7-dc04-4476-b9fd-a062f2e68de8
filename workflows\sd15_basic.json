{"1": {"inputs": {"ckpt_name": "v1-5-pruned-em<PERSON><PERSON><PERSON>.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load SD1.5 Checkpoint"}}, "2": {"inputs": {"text": "a beautiful landscape with mountains and a lake, highly detailed, masterpiece, best quality, 8k", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "3": {"inputs": {"text": "blurry, low quality, bad anatomy, worst quality, low resolution, watermark", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative Prompt"}}, "4": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "5": {"inputs": {"seed": 123456, "steps": 25, "cfg": 7.5, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 1.0, "model": ["1", 0], "positive": ["2", 0], "negative": ["3", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"filename_prefix": "SD15_", "images": ["6", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}