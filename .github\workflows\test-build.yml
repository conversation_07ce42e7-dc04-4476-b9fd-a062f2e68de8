name: Build package

#
# This workflow is a test of the python package build.
# Install Python dependencies across different Python versions.
#

on:
  push:
    paths:
      - "requirements.txt"
      - ".github/workflows/test-build.yml"

jobs:
  build:
    name: Build Test
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
