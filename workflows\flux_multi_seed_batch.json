{"1": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Flux UNet"}}, "2": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "Load Flux Dual CLIP"}}, "3": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load Flux VAE"}}, "4": {"inputs": {"text": "a professional portrait photograph of a beautiful woman, natural lighting, shallow depth of field, photorealistic, highly detailed, 8k resolution, masterpiece, best quality", "clip": ["2", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "5": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Single Latent"}}, "6": {"inputs": {"noise": ["7", 0], "guider": ["8", 0], "sampler": ["9", 0], "sigmas": ["10", 0], "latent_image": ["5", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "Sampler 1"}}, "7": {"inputs": {"seed": 42}, "class_type": "RandomNoise", "_meta": {"title": "Seed 1"}}, "8": {"inputs": {"model": ["1", 0], "conditioning": ["4", 0]}, "class_type": "BasicGuider", "_meta": {"title": "Basic Guider"}}, "9": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "Sampler Select"}}, "10": {"inputs": {"scheduler": "simple", "steps": 25, "denoise": 1.0, "model": ["1", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "Scheduler"}}, "11": {"inputs": {"samples": ["6", 0], "vae": ["3", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode 1"}}, "12": {"inputs": {"noise": ["13", 0], "guider": ["8", 0], "sampler": ["9", 0], "sigmas": ["10", 0], "latent_image": ["5", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "Sampler 2"}}, "13": {"inputs": {"seed": 123}, "class_type": "RandomNoise", "_meta": {"title": "Seed 2"}}, "14": {"inputs": {"samples": ["12", 0], "vae": ["3", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode 2"}}, "15": {"inputs": {"image1": ["11", 0], "image2": ["14", 0]}, "class_type": "ImageBatch", "_meta": {"title": "Combine Images"}}, "16": {"inputs": {"filename_prefix": "Flux_MultiSeed_", "images": ["15", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save All Images"}}}