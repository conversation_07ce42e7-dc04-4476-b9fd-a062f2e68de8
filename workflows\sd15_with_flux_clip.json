{"1": {"inputs": {"ckpt_name": "v1-5-pruned-em<PERSON><PERSON><PERSON>.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load SD1.5 Checkpoint"}}, "2": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "Load Flux CLIP (Better Text Understanding)"}}, "3": {"inputs": {"text": "a beautiful landscape with mountains and a lake, highly detailed, photorealistic, masterpiece, best quality, 8k", "clip": ["2", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt (Flux CLIP)"}}, "4": {"inputs": {"text": "blurry, low quality, bad anatomy, worst quality, low resolution, watermark, signature, ugly", "clip": ["2", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative Prompt (Flux CLIP)"}}, "5": {"inputs": {"width": 768, "height": 768, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"seed": 42, "steps": 25, "cfg": 7.5, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1.0, "model": ["1", 0], "positive": ["3", 0], "negative": ["4", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "KSampler (SD1.5 Model)"}}, "7": {"inputs": {"samples": ["6", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "8": {"inputs": {"filename_prefix": "SD15_FluxCLIP_", "images": ["7", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}