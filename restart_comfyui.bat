@echo off
title ComfyUI 重启脚本
color 0E
chcp 65001 >nul

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ComfyUI 重启脚本                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/3] 正在停止现有的ComfyUI进程...
taskkill /f /im python.exe 2>nul
if %errorlevel%==0 (
    echo ✓ 已停止现有进程
) else (
    echo ℹ 没有发现运行中的进程
)

echo.
echo [2/3] 等待进程完全停止...
timeout /t 3 >nul

echo.
echo [3/3] 重新启动ComfyUI...
echo.
echo ═══════════════════════════════════════
echo 服务地址: http://127.0.0.1:8188
echo 启动完成后将自动打开浏览器
echo 按 Ctrl+C 停止服务
echo ═══════════════════════════════════════
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe main.py --auto-launch

echo.
echo ═══════════════════════════════════════
echo ComfyUI 服务已停止
echo ═══════════════════════════════════════
pause
