{"1": {"inputs": {"ckpt_name": "sd3.5_large.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load SD3.5 Large"}}, "2": {"inputs": {"text": "a professional portrait photograph of a beautiful woman, natural lighting, shallow depth of field, photorealistic, highly detailed, 8k resolution, masterpiece, best quality", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "3": {"inputs": {"text": "blurry, low quality, bad anatomy, worst quality, low resolution, watermark, signature, ugly, distorted", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative Prompt"}}, "4": {"inputs": {"width": 1024, "height": 1024, "batch_size": 6}, "class_type": "EmptyLatentImage", "_meta": {"title": "<PERSON><PERSON> (6 Images)"}}, "5": {"inputs": {"seed": 42, "steps": 20, "cfg": 4.0, "sampler_name": "euler", "scheduler": "sgm_uniform", "denoise": 1.0, "model": ["1", 0], "positive": ["2", 0], "negative": ["3", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON>"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"filename_prefix": "SD35_Batch_", "images": ["6", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Batch Images"}}}